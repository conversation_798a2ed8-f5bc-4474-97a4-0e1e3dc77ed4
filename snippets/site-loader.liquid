<!-- Site Loader -->
<style>
  .fuzzyOverlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    opacity: 0.03;
    z-index: 1;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIiB4PSIwIiB5PSIwIj48ZmVUdXJidWxlbmNlIGJhc2VGcmVxdWVuY3k9Ii43NSIgc3RpdGNoVGlsZXM9InN0aXRjaCIgdHlwZT0iZnJhY3RhbE5vaXNlIi8+PGZlQ29sb3JNYXRyaXggdHlwZT0ic2F0dXJhdGUiIHZhbHVlcz0iMCIvPjwvZmlsdGVyPjxwYXRoIGQ9Ik0wIDBoMzAwdjMwMEgweiIgZmlsdGVyPSJ1cmwoI2EpIiBvcGFjaXR5PSIuMDUiLz48L3N2Zz4=');
  }
  
  .loaderAnimCust {
    width: 80px;
    height: 25px;
    border-radius: 20px;
    color: #e7e8e6;
    border: 2px solid;
    position: relative;
    animation: fadeOutLoaderMain 1s;
    animation-delay: 1.2s;
  }

  @keyframes fadeOutLoaderMain {
    0% {
      scale: 1.08;
    }
    100% {
      scale: .5;
    }
  }

  .loaderAnimCust::before {
    content: "";
    position: absolute;
    margin: 2px;
    inset: 0 100% 0 0;
    border-radius: inherit;
    background: currentColor;
    animation: l6 2s infinite;
  }
  @keyframes l6 {
      100% {
        inset:0;
           }
  }

  @keyframes fadeOutLoader {
    0% {
      opacity: 1;
    }
      50%{
        opacity: .5;
      }
    100% {
      opacity: 0;
    }
  }
</style>

<script>
function shouldShowLoader() {
  // Don't show loader on activate page
  if (window.location.pathname.includes('/pages/activate') ||
      window.location.pathname.includes('/activate') ||
      document.body.querySelector('[data-youform-embed]')) {
    return false;
  }

  // Check if this is the first time the user is visiting the site in this session
  const hasVisitedBefore = sessionStorage.getItem('siteVisited');

  // Get current domain and referring domain
  const currentDomain = window.location.hostname;
  let referringDomain = '';

  if (document.referrer) {
    // Extract domain from referrer
    const referrerUrl = new URL(document.referrer);
    referringDomain = referrerUrl.hostname;
  }

  // Check if user is coming from an external site
  const isExternalReferrer = referringDomain && referringDomain !== currentDomain;

  // Show loader if it's first visit or coming from external site
  return !hasVisitedBefore || isExternalReferrer;
}

function createLoader() {
    let loader = document.createElement('div');
    loader.id = 'custLoader';
    loader.style.cssText = `
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      width: 100vw;
      z-index: 999999;
      position: fixed;
      top: 0;
      left: 0;
      background: #2a2a2a;
    `;
    loader.innerHTML = `
      <div class="loaderAnimCust"></div>
    `;
    document.querySelector('body').prepend(loader);
    document.body.style.overflow = 'hidden';
    
    // Mark the site as visited for this session
    sessionStorage.setItem('siteVisited', 'true');
    
    setTimeout(() => {
        const loader = document.querySelector('#custLoader');
        loader.style.animation = 'fadeOutLoader .5s forwards';
        setTimeout(() => {
            loader.remove();
            document.body.style.overflow = '';
        }, 100);
    }, 1500);
}

// Only create the loader if conditions are met
if (shouldShowLoader()) {
    createLoader();
}
</script>

<script>
  function createWhiteNoise() {
    let div = document.createElement('div');
    div.className = "fuzzyOverlay";
    let body = document.querySelector('body');
    body.appendChild(div);
  }

  // Only create white noise effect if loader conditions are met
  if (shouldShowLoader()) {
    createWhiteNoise();
  }
</script>
